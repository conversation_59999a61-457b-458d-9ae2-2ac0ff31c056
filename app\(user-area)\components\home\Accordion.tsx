import { cn } from "@/lib/utils";
import { ChevronDown, ChevronUpIcon } from "lucide-react";
import React, { useState } from "react";

interface AccordionItem {
  question: string;
  answer: string;
  listItems?: string[];
}

interface AccordionProps {
  items: AccordionItem[];
}

const Accordion: React.FC<AccordionProps> = ({ items }) => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [showAll, setShowAll] = useState<boolean>(false);

  const toggleItem = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  const toggleShowAll = () => {
    setShowAll(!showAll);
  };
  return (
    <div className="sm:max-w-lg lg:max-w-full mx-auto border rounded-xl shadow-sm mb-20 mt-6">
      {items.slice(0, showAll ? items.length : 5).map((item, index) => (
        <div key={index} className="rounded-lg mb-4">
          <div
            className="w-full text-left py-4 px-6 font-medium text-gray-800 flex justify-between items-center focus:outline-none space-x-8 cursor-pointer"
            onClick={() => toggleItem(index)}
          >
            <span
              className={`text-neutral-800 text-sm ${
                openIndex === index && "text-[#FF5F5F]"
              }`}
            >
              {item.question}
            </span>
            <div>
              {openIndex === index ? (
                <ChevronUpIcon className="h-4 w-4 text-neutral-800 rotate-180 text-[#FF5F5F]" />
              ) : (
                <ChevronDown className="h-4 w-4 text-neutral-800" />
              )}
            </div>
          </div>
          {openIndex === index && (
            <div className="px-6 py-4 text-sm">
              <p className="text-neutral-700">{item.answer}</p>
              <div className="mt-2">
                {item.listItems?.map((i) => (
                  <div key={i} className="text-sm text-neutral-700">
                    {i}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
      {items.length > 4 && (
        <button
          className="block mx-auto mt-4 mb-2 px-4 py-2 text-sm ring-1 text-neutral-800  ring-[#FF5F5F] rounded-lg focus:outline-none hover:bg-neutral-100"
          onClick={toggleShowAll}
        >
          {showAll ? "View Less" : "More FAQ's"}
        </button>
      )}
    </div>
  );
};

export default Accordion;
