"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

const SearchPackage = () => {
  const router = useRouter();

  const navigate = (path: string) => {
    router.push(path);
  };

  return (
    <section id="hero-section" className="relative w-full h-[500px] sm:h-[600px] min-h-[500px] sm:min-h-[600px] flex flex-col items-center justify-center overflow-hidden">
      {/* Background with a more vibrant and inviting travel image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat h-full"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1512100356356-de1b84283e18?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/70 h-full"></div>
      </div>
      
      {/* Content */}
      <div className="relative z-10 text-center px-4 max-w-5xl mx-auto">
        <h1 className="text-4xl md:text-7xl font-semibold text-white mb-4 leading-tight animate-fade-in-up">
          Seamless Travel, <span className="text-emerald-400 font-extrabold italic">Unforgettable</span> Experiences
        </h1>
        
        {/* Enhanced Search Bar */}
        <div className="max-w-2xl mx-auto mb-12 mt-8">
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400 to-blue-500 rounded-full blur opacity-70 group-hover:opacity-90 transition duration-500 animate-pulse-slow"></div>
            <div className="relative bg-white/95 backdrop-blur-sm rounded-full p-1 sm:p-2 shadow-2xl">
              <div className="flex items-center">
                <Search className="ml-4 sm:ml-6 text-gray-400 w-6 h-6" />
                <Input 
                  placeholder="Search destinations..."
                  className="flex-1 border-0 bg-transparent px-2 sm:px-4 py-2 sm:py-4 text-sm sm:text-lg focus:ring-0 placeholder:text-gray-500 md:hidden focus-visible:ring-0 focus-visible:ring-offset-0"
                  onClick={() => navigate("/search-progress")}
                />
                <Input 
                  placeholder="Where to next? Search destinations..."
                  className="flex-1 border-0 bg-transparent px-2 sm:px-4 py-2 sm:py-4 text-sm sm:text-lg focus:ring-0 placeholder:text-gray-500 hidden md:block focus-visible:ring-0 focus-visible:ring-offset-0"
                  onClick={() => navigate("/search-progress")}
                />
                <Button 
                  onClick={() => navigate("/search-progress")}
                  className="bg-emerald-500 hover:bg-emerald-600 text-white px-4 sm:px-8 py-3 rounded-full font-semibold shadow-lg transition-all duration-300 hover:scale-105 hidden md:block"
                >
                  Explore
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Enhanced floating elements for a more dynamic feel */}
      <div className="absolute top-20 left-10 w-28 h-28 bg-emerald-400/20 backdrop-blur-sm rounded-full animate-pulse-slow hidden lg:block"></div>
      <div className="absolute bottom-32 right-16 w-24 h-24 bg-blue-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-700 hidden lg:block"></div>
      <div className="absolute top-1/3 right-24 w-20 h-20 bg-purple-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-300 hidden lg:block"></div>
      <div className="absolute bottom-1/4 left-24 w-16 h-16 bg-yellow-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-1000 hidden lg:block"></div>
    </section>
  );
};

export default SearchPackage;
