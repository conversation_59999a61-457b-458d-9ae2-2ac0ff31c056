"use client";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import Paragraph from "@/components/ui/paragraph";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Facebook, Instagram, XIcon } from "lucide-react";
import Link from "next/link";
import React, { HTMLAttributes, useEffect, useRef, useState } from "react";
import PrivacyPolicy from "./PrivacyPolicy";

interface FooterProps {
  ref: React.RefObject<HTMLDivElement>;
}
const Footer = () => {
  return (
    <>
      <div className="sm:flex sm:justify-between sm:items-center lg:flex  lg:items-center lg:justify-between lg:px-[197px]">
        <div className="flex flex-col items-start  gap-3">
          <h1 className="text-[#FF5F5F] text-lg lg:text-2xl lg:font-semibold">
            Navigate
          </h1>
          <div className="flex-col flex space-y-3 text-sm items-start">
            {/* About Tripxplo */}

            <AlertDialog>
              <AlertDialogTrigger className="">
                About Tripxplo
              </AlertDialogTrigger>

              <AlertDialogContent className="w-4/5">
                <h1 className="text-xl text-[#FF7865]">Tripxplo</h1>
                <h1 className="text-sm mt-3">
                  TripXplo, registered as Tripmilestone Tours Pvt. Ltd, was
                  founded in 2020 by a team of young travel enthusiasts. A
                  Travel Company for travellers that have big destinations in
                  mind and huge milestones to traverse yet low on pocket.
                </h1>
                <AlertDialogFooter>
                  <AlertDialogCancel>Close</AlertDialogCancel>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
            {/* Privacy Policy */}
            <Link href="/privacypolicy">Privacy Policy</Link>

            {/* Refund Policy  */}
            <Link href="/refundpolicy">Cancellations & Refund Policy</Link>

            {/* FAQ */}
            <Link href={"#faq"}>FAQ</Link>
            {/* Terms and Conditions */}
            <Link href="/termsandconditions">Terms & Conditions</Link>
          </div>
        </div>
        <div className="flex flex-col items-center">
          <h1 className="text-[#FF5F5F] text-lg lg:text-2xl lg:font-semibold">
            Follow us
          </h1>
          <div className="flex items-center justify-center space-x-4 mt-2 text-neutral-800">
            <Link
              href={
                "https://www.instagram.com/mytripxplo/?utm_source=ig_web_button_share_sheet"
              }
            >
              <Facebook size={18} />
            </Link>

            <Link
              href={
                "https://www.instagram.com/mytripxplo/?utm_source=ig_web_button_share_sheet"
              }
            >
              <Instagram size={18} />
            </Link>

            <Link
              href={
                "https://www.instagram.com/mytripxplo/?utm_source=ig_web_button_share_sheet"
              }
            >
              <XIcon size={18} />
            </Link>
          </div>
        </div>
      </div>

      <div className=" flex items-center flex-col gap-1 mt-32">
        <h1 className="text-center text-sm  lg:text-lg">
          Made with ❤️ in India{" "}
        </h1>
        <span className="text-[#FF5F5F]  ">
          ©TripXplo by Tripmilestone (P) Ltd.
        </span>
      </div>
    </>
  );
};

export default Footer;
