import { initialLoad } from "@/app/store/features/roomCapacitySlice";
import {
  changeDate,
  changeDestination,
  changeDestinationId,
} from "@/app/store/features/searchPackageSlice";
import { MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import test from "node:test";
import React from "react";
import { useDispatch } from "react-redux";

interface GalleryItemProps {
  src: string;
  alt: string;
  text: string;
  featured?: boolean;
  destinationId: string;
}

const GalleryItemMobile: React.FC<GalleryItemProps> = ({
  src,
  alt,
  text,
  destinationId,
}) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const handleClick = (text: string, destinationId: string) => {
    dispatch(changeDestination(text));
    dispatch(changeDestinationId(destinationId));
    // Calculate date 10 days from now
    const newDate = new Date();
    newDate.setDate(newDate.getDate() + 10);

    // Convert to local date
    const localDate = new Date(
      newDate.getTime() - newDate.getTimezoneOffset() * 60000
    );

    // Dispatch the date
    dispatch(changeDate(localDate.toISOString()));
    dispatch(initialLoad());
    router.push(`/packages`);
  };
  return (
    <div
      className="sm:relative lg:hidden overflow-hidden rounded-2xl bg-black group cursor-pointer"
      onClick={() => handleClick(text, destinationId)}
    >
      <Image
        src={src}
        alt={alt}
        width={100}
        height={100}
        layout="responsive"
        objectFit="cover"
        className="transition-transform duration-300 ease-in-out transform group-hover:scale-105"
      />
      <div className="absolute inset-0 flex flex-col justify-between p-4 bg-gradient-to-t from-black/70 to-transparent">
        <div className="flex-1 flex items-center justify-center">
          <div className="px-2 py-1 bg-white text-center text-[#FF5F5F] text-xs rounded-lg font-normal opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            Explore Packages
          </div>
        </div>
        <p className="flex font-bold items-center gap-2 text-white text-xs [text-shadow:_2px_2px_4px_rgb(0_0_0_/_70%)]">
          <MapPin size={18} />
          {text}
        </p>
      </div>
    </div>
  );
};

const GalleryItem: React.FC<GalleryItemProps> = ({
  src,
  alt,
  text,
  featured,
  destinationId,
}) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const handleClick = (text: string, destinationId: string) => {
    dispatch(changeDestination(text));
    dispatch(changeDestinationId(destinationId));
    // Calculate date 10 days from now
    const newDate = new Date();
    newDate.setDate(newDate.getDate() + 10);

    // Convert to local date
    const localDate = new Date(
      newDate.getTime() - newDate.getTimezoneOffset() * 60000
    );

    // Dispatch the date
    dispatch(changeDate(localDate.toISOString()));
    dispatch(initialLoad());
    router.push(`/packages`);
  };
  return (
    <div
      className={`relative overflow-hidden rounded-2xl bg-black group ${
        featured ? "h-full" : "h-64"
      }`}
    >
      <Image
        src={src}
        alt={alt}
        fill
        className="object-cover transition-transform duration-300 ease-in-out transform group-hover:scale-105"
      />
      <h1 className="text-xl font-bold flex items-center gap-2 text-white absolute bottom-4 left-4 [text-shadow:_2px_2px_4px_rgb(0_0_0_/_70%)]">
        <MapPin size={18} /> {text}
      </h1>
      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 text-center text-lg font-semibold opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100">
        <div
          onClick={() => handleClick(text, destinationId)}
          // href={""}
          className="px-3 py-2 bg-white text-[#FF5F5F] rounded-sm font-normal cursor-pointer"
        >
          Explore Packages
        </div>
      </div>
    </div>
  );
};

interface GalleryProps {
  items: GalleryItemProps[];
}

const DestinationGallery: React.FC<GalleryProps> = ({ items }) => {
  console.log(items, "items");
  // Mobile and tablet view
  const mobileView = (
    <div className="mt-2 w-full lg:hidden block max-w-5xl p-5 pb-10 mx-auto mb-10 gap-5 sm:columns-2 space-y-5">
      {items.map((item, index) => (
        <GalleryItemMobile
          key={index}
          src={item.src}
          alt={item.alt}
          text={item.text}
          destinationId={item.destinationId}
        />
      ))}
    </div>
  );

  // Desktop view with masonry-style layout
  const desktopView = (
    <div className="hidden lg:block px-4 mt-8">
      <div className="grid grid-cols-12 gap-7">
        {/* Left column */}
        <div className="col-span-3 space-y-6">
          <GalleryItem
            src={items[0].src}
            alt={items[0].alt}
            text={items[0].text}
            destinationId={items[0].destinationId}
          />
          <GalleryItem
            src={items[1].src}
            alt={items[1].alt}
            text={items[1].text}
            destinationId={items[1].destinationId}
          />
        </div>

        {/* Center column (Malaysia) */}
        <div className="col-span-3 h-[540px]">
          <GalleryItem
            src={items[2].src}
            alt={items[2].alt}
            text={items[2].text}
            featured
            destinationId={items[2].destinationId}
          />
        </div>

        {/* Right column */}
        <div className="col-span-3 space-y-6">
          <GalleryItem
            src={items[3].src}
            alt={items[3].alt}
            text={items[3].text}
            destinationId={items[3].destinationId}
          />
          <GalleryItem
            src={items[4].src}
            alt={items[4].alt}
            text={items[4].text}
            destinationId={items[4].destinationId}
          />
        </div>
        <div className="col-span-3 h-[540px]">
          <GalleryItem
            src={items[5].src}
            alt={items[5].alt}
            text={items[5].text}
            featured
            destinationId={items[5].destinationId}
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className="w-full">
      {mobileView}
      {desktopView}
    </div>
  );
};

export default DestinationGallery;
